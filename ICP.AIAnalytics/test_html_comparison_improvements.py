#!/usr/bin/env python3
"""
Test script to demonstrate the improved HTML comparison service.
This script tests the enhanced error handling, JSON parsing, and debugging features.
"""

import asyncio
import json
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Sample HTML content for testing
SAMPLE_HTML_1 = """
<html>
<head><title>Order Summary</title></head>
<body>
    <div class="order-container">
        <h1>Order #12345</h1>
        <table class="order-table">
            <tr>
                <td>Product</td>
                <td>Quantity</td>
                <td>Price</td>
            </tr>
            <tr>
                <td>Widget A</td>
                <td>2</td>
                <td>$10.00</td>
            </tr>
        </table>
        <div class="total">Total: $20.00</div>
    </div>
</body>
</html>
"""

SAMPLE_HTML_2 = """
<html>
<head><title>Order Summary - Updated</title></head>
<body>
    <div class="order-container">
        <h1>Order #12345</h1>
        <table class="order-table">
            <tr>
                <td>Product</td>
                <td>Quantity</td>
                <td>Price</td>
            </tr>
            <tr>
                <td>Widget A</td>
                <td>3</td>
                <td>$10.00</td>
            </tr>
            <tr>
                <td>Widget B</td>
                <td>1</td>
                <td>$15.00</td>
            </tr>
        </table>
        <div class="total">Total: $45.00</div>
        <div class="status">Status: Confirmed</div>
    </div>
</body>
</html>
"""

# Malformed HTML for testing error handling
MALFORMED_HTML = """
<html>
<head><title>Broken HTML</title>
<body>
    <div class="container">
        <h1>Missing closing tags
        <p>This HTML is intentionally malformed
        <table>
            <tr><td>Cell 1<td>Cell 2
        </table>
    </div>
"""

async def test_html_comparison_service():
    """Test the HTML comparison service with various scenarios."""
    try:
        # Import here to avoid import issues if running outside the app context
        from app.services.html_comparison_service import HTMLComparisonService
        from app.models.schemas import HTMLComparisonRequest
        
        # Initialize service
        service = HTMLComparisonService()
        logger.info("HTML Comparison Service initialized")
        
        # Test 1: Normal comparison
        logger.info("=== Test 1: Normal HTML Comparison ===")
        request1 = HTMLComparisonRequest(
            requested_order_html=SAMPLE_HTML_1,
            finalized_order_html=SAMPLE_HTML_2
        )
        
        result1 = await service.compare_html(request1)
        logger.info(f"Comparison ID: {result1.comparison_id}")
        logger.info(f"Summary: {result1.summary}")
        logger.info(f"Total changes: {result1.total_changes}")
        logger.info(f"Confidence score: {result1.confidence_score}")
        logger.info(f"Processing time: {result1.processing_time:.2f}s")
        
        # Test 2: Malformed HTML handling
        logger.info("\n=== Test 2: Malformed HTML Handling ===")
        request2 = HTMLComparisonRequest(
            requested_order_html=SAMPLE_HTML_1,
            finalized_order_html=MALFORMED_HTML
        )
        
        result2 = await service.compare_html(request2)
        logger.info(f"Comparison ID: {result2.comparison_id}")
        logger.info(f"Summary: {result2.summary}")
        logger.info(f"Total changes: {result2.total_changes}")
        logger.info(f"Confidence score: {result2.confidence_score}")
        
        # Test 3: Empty HTML handling
        logger.info("\n=== Test 3: Empty HTML Handling ===")
        request3 = HTMLComparisonRequest(
            requested_order_html="",
            finalized_order_html=SAMPLE_HTML_1
        )
        
        result3 = await service.compare_html(request3)
        logger.info(f"Comparison ID: {result3.comparison_id}")
        logger.info(f"Summary: {result3.summary}")
        logger.info(f"Total changes: {result3.total_changes}")
        
        # Test 4: Debug information
        logger.info("\n=== Test 4: Debug Information ===")
        debug_info = service.get_comparison_debug_info(result1.comparison_id)
        logger.info("Debug information:")
        logger.info(json.dumps(debug_info, indent=2, default=str))
        
        # Test 5: Large HTML handling
        logger.info("\n=== Test 5: Large HTML Handling ===")
        large_html = SAMPLE_HTML_1 * 1000  # Create very large HTML
        request5 = HTMLComparisonRequest(
            requested_order_html=large_html,
            finalized_order_html=SAMPLE_HTML_2
        )
        
        result5 = await service.compare_html(request5)
        logger.info(f"Large HTML comparison completed")
        logger.info(f"Summary: {result5.summary}")
        logger.info(f"Processing time: {result5.processing_time:.2f}s")
        
        logger.info("\n=== All Tests Completed Successfully ===")
        
    except Exception as e:
        logger.error(f"Test failed with error: {e}", exc_info=True)

def test_json_parsing():
    """Test the improved JSON parsing logic."""
    logger.info("\n=== Testing JSON Parsing Improvements ===")
    
    # Test cases for JSON parsing
    test_cases = [
        # Valid JSON
        '{"summary": "Test", "total_changes": 1, "confidence_score": 0.8, "added_elements": [], "removed_elements": [], "modified_elements": []}',
        
        # JSON with markdown code blocks
        '```json\n{"summary": "Test", "total_changes": 1, "confidence_score": 0.8, "added_elements": [], "removed_elements": [], "modified_elements": []}\n```',
        
        # JSON with extra whitespace
        '   \n  {"summary": "Test", "total_changes": 1, "confidence_score": 0.8, "added_elements": [], "removed_elements": [], "modified_elements": []}  \n  ',
        
        # Invalid JSON
        '{"summary": "Test", "total_changes": 1, "confidence_score": 0.8, "added_elements": [], "removed_elements": [], "modified_elements":',
        
        # Non-JSON response
        'This is not JSON at all, just plain text response from the LLM.'
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        logger.info(f"Test case {i}:")
        try:
            # Simulate the cleaning logic from the service
            cleaned = test_case.strip()
            if cleaned.startswith('```json'):
                cleaned = cleaned[7:]
            if cleaned.startswith('```'):
                cleaned = cleaned[3:]
            if cleaned.endswith('```'):
                cleaned = cleaned[:-3]
            cleaned = cleaned.strip()
            
            parsed = json.loads(cleaned)
            logger.info(f"  ✓ Successfully parsed: {type(parsed)}")
            
        except json.JSONDecodeError as e:
            logger.info(f"  ✗ JSON parsing failed: {e}")
        except Exception as e:
            logger.info(f"  ✗ Unexpected error: {e}")

if __name__ == "__main__":
    logger.info("Starting HTML Comparison Service Tests")
    logger.info(f"Test started at: {datetime.now()}")
    
    # Test JSON parsing improvements
    test_json_parsing()
    
    # Test the full service (requires app context)
    try:
        asyncio.run(test_html_comparison_service())
    except ImportError as e:
        logger.warning(f"Could not import app modules: {e}")
        logger.info("This is expected when running outside the FastAPI app context")
    except Exception as e:
        logger.error(f"Service test failed: {e}")
    
    logger.info(f"Test completed at: {datetime.now()}")
