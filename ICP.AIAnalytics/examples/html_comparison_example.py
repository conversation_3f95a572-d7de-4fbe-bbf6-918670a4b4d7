#!/usr/bin/env python3
"""
Example usage of the HTML Comparison API.

This script demonstrates how to use the HTML comparison endpoints
to compare two HTML documents and get detailed analysis of changes.
"""

import httpx
import asyncio
import json
from datetime import datetime


async def example_html_comparison():
    """Example of using the HTML comparison API."""
    
    print("HTML Comparison API Example")
    print("=" * 50)
    
    # Base URL for the API
    base_url = "http://localhost:8000/api/v1/html"
    
    # Sample HTML documents representing order changes
    requested_order_html = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <title>Order Request #ORD-2024-001</title>
        <style>
            .order-container { margin: 20px; padding: 15px; border: 1px solid #ccc; }
            .customer-info { background: #f9f9f9; padding: 10px; margin: 10px 0; }
            .order-items { margin: 15px 0; }
            .total { font-weight: bold; background: #e9e9e9; padding: 10px; }
        </style>
    </head>
    <body>
        <div class="order-container">
            <h1>Order Request #ORD-2024-001</h1>
            <div class="customer-info">
                <h2>Customer Information</h2>
                <p><strong>Name:</strong> Alice Johnson</p>
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Account Type:</strong> Premium</p>
            </div>
            <div class="order-items">
                <h2>Requested Items</h2>
                <ul>
                    <li>Laptop Computer - Model XYZ123 - $1,200.00</li>
                    <li>Wireless Mouse - $25.00</li>
                    <li>USB Cable - $15.00</li>
                </ul>
            </div>
            <div class="total">
                <p>Subtotal: $1,240.00</p>
                <p>Estimated Total: $1,240.00</p>
            </div>
            <div class="notes">
                <p><em>Note: Final pricing subject to availability and current promotions.</em></p>
            </div>
        </div>
    </body>
    </html>
    """
    
    finalized_order_html = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <title>Finalized Order #ORD-2024-001</title>
        <style>
            .order-container { margin: 20px; padding: 15px; border: 1px solid #ccc; }
            .customer-info { background: #f9f9f9; padding: 10px; margin: 10px 0; }
            .order-items { margin: 15px 0; }
            .total { font-weight: bold; background: #e9e9e9; padding: 10px; }
            .shipping-info { background: #f0f8ff; padding: 10px; margin: 10px 0; }
            .approved { color: green; font-weight: bold; }
        </style>
    </head>
    <body>
        <div class="order-container">
            <h1>Finalized Order #ORD-2024-001</h1>
            <div class="status approved">
                <p>✓ ORDER APPROVED AND CONFIRMED</p>
            </div>
            <div class="customer-info">
                <h2>Customer Information</h2>
                <p><strong>Name:</strong> Alice Johnson</p>
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Phone:</strong> ******-0199</p>
                <p><strong>Account Type:</strong> Premium</p>
                <p><strong>Customer ID:</strong> CUST-789456</p>
            </div>
            <div class="order-items">
                <h2>Confirmed Items</h2>
                <ul>
                    <li>Laptop Computer - Model XYZ123 - $1,150.00 <span class="discount">(10% discount applied)</span></li>
                    <li>Wireless Mouse - $25.00</li>
                    <li>USB Cable - $15.00</li>
                    <li>Extended Warranty - $99.00 <span class="new-item">(Added)</span></li>
                </ul>
            </div>
            <div class="total">
                <p>Subtotal: $1,289.00</p>
                <p>Premium Discount: -$115.00</p>
                <p>Tax (8.5%): $99.79</p>
                <p><strong>Final Total: $1,273.79</strong></p>
            </div>
            <div class="shipping-info">
                <h2>Shipping Information</h2>
                <p><strong>Address:</strong> 456 Oak Avenue, Suite 12B, Springfield, IL 62701</p>
                <p><strong>Method:</strong> Express Shipping (2-3 business days)</p>
                <p><strong>Tracking:</strong> Will be provided via email</p>
            </div>
            <div class="notes">
                <p><em>Order confirmed on {datetime.now().strftime('%B %d, %Y')}. Thank you for your business!</em></p>
            </div>
        </div>
    </body>
    </html>
    """
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            
            # 1. Test health check
            print("1. Checking service health...")
            health_response = await client.get(f"{base_url}/health")
            if health_response.status_code == 200:
                health_data = health_response.json()
                print(f"   ✓ Service Status: {health_data['status']}")
                print(f"   ✓ LLM Available: {health_data['llm_available']}")
            else:
                print(f"   ✗ Health check failed: {health_response.status_code}")
                return
            
            # 2. Perform full comparison
            print("\n2. Performing full HTML comparison...")
            
            comparison_data = {
                "requested_order_html": requested_order_html,
                "finalized_order_html": finalized_order_html
            }
            
            comparison_response = await client.post(
                f"{base_url}/compare", 
                json=comparison_data
            )
            
            if comparison_response.status_code == 200:
                result = comparison_response.json()
                
                print(f"   ✓ Comparison completed successfully!")
                print(f"   ✓ Comparison ID: {result['comparison_id']}")
                print(f"   ✓ Total Changes: {result['total_changes']}")
                print(f"   ✓ Processing Time: {result['processing_time']:.2f} seconds")
                print(f"   ✓ Confidence Score: {result['confidence_score']}")
                print(f"   ✓ Summary: {result['summary']}")
                
                # Display detailed changes
                print(f"\n   Added Elements ({len(result['added_elements'])}):")
                for i, element in enumerate(result['added_elements'][:5], 1):  # Show first 5
                    print(f"      {i}. {element['description']}")
                    if element.get('location'):
                        print(f"         Location: {element['location']}")
                
                print(f"\n   Removed Elements ({len(result['removed_elements'])}):")
                for i, element in enumerate(result['removed_elements'][:5], 1):  # Show first 5
                    print(f"      {i}. {element['description']}")
                
                print(f"\n   Modified Elements ({len(result['modified_elements'])}):")
                for i, element in enumerate(result['modified_elements'][:5], 1):  # Show first 5
                    print(f"      {i}. {element['description']}")
                    if element.get('old_value') and element.get('new_value'):
                        print(f"         Old: {element['old_value'][:50]}...")
                        print(f"         New: {element['new_value'][:50]}...")
                
            else:
                print(f"   ✗ Comparison failed: {comparison_response.status_code}")
                print(f"   Error: {comparison_response.text}")
                return
            
            # 3. Test summary endpoint
            print("\n3. Getting comparison summary...")
            
            summary_response = await client.post(
                f"{base_url}/compare/summary",
                json=comparison_data
            )
            
            if summary_response.status_code == 200:
                summary = summary_response.json()
                print(f"   ✓ Summary retrieved successfully!")
                print(f"   ✓ Total Changes: {summary['total_changes']}")
                print(f"   ✓ Changes Breakdown:")
                breakdown = summary['changes_breakdown']
                print(f"      - Added: {breakdown['added']}")
                print(f"      - Removed: {breakdown['removed']}")
                print(f"      - Modified: {breakdown['modified']}")
                print(f"   ✓ Processing Time: {summary['processing_time']:.2f} seconds")
            else:
                print(f"   ✗ Summary failed: {summary_response.status_code}")
            
            print("\n" + "=" * 50)
            print("✓ All tests completed successfully!")
            print("\nThe HTML Comparison API is working correctly and can:")
            print("- Compare complex HTML documents")
            print("- Identify structural and content changes")
            print("- Provide detailed analysis with AI insights")
            print("- Return both full and summary results")
            
    except httpx.ConnectError:
        print("✗ Could not connect to the API server.")
        print("Make sure the FastAPI server is running on http://localhost:8000")
    except Exception as e:
        print(f"✗ An error occurred: {str(e)}")


if __name__ == "__main__":
    print("Starting HTML Comparison API Example...")
    print("Make sure the FastAPI server is running: uvicorn app.main:app --reload")
    print()
    
    asyncio.run(example_html_comparison())
