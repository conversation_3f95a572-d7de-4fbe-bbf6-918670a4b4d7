#!/usr/bin/env python3
"""
Test script for HTML comparison functionality.
"""

import sys
import os
import asyncio
import json

# Add the app directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.models.schemas import HTMLComparisonRequest
from app.services.html_comparison_service import HTMLComparisonService


async def test_html_comparison():
    """Test the HTML comparison service with sample data."""
    
    print("Testing HTML Comparison Service")
    print("=" * 50)
    
    # Sample HTML documents for testing
    requested_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Order Request</title>
    </head>
    <body>
        <div class="order-container">
            <h1>Order #12345</h1>
            <div class="customer-info">
                <p>Customer: <PERSON></p>
                <p>Email: <EMAIL></p>
            </div>
            <div class="order-items">
                <ul>
                    <li>Product A - $100</li>
                    <li>Product B - $50</li>
                </ul>
            </div>
            <div class="total">
                <p>Total: $150</p>
            </div>
        </div>
    </body>
    </html>
    """
    
    finalized_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Finalized Order</title>
    </head>
    <body>
        <div class="order-container">
            <h1>Order #12345</h1>
            <div class="customer-info">
                <p>Customer: John Doe</p>
                <p>Email: <EMAIL></p>
                <p>Phone: ******-0123</p>
            </div>
            <div class="order-items">
                <ul>
                    <li>Product A - $100</li>
                    <li>Product B - $50</li>
                    <li>Product C - $25</li>
                </ul>
            </div>
            <div class="total">
                <p>Subtotal: $175</p>
                <p>Tax: $17.50</p>
                <p>Total: $192.50</p>
            </div>
            <div class="shipping-info">
                <p>Shipping Address: 123 Main St, City, State 12345</p>
                <p>Estimated Delivery: 3-5 business days</p>
            </div>
        </div>
    </body>
    </html>
    """
    
    try:
        # Initialize the service
        service = HTMLComparisonService()
        
        # Create request
        request = HTMLComparisonRequest(
            requested_order_html=requested_html,
            finalized_order_html=finalized_html
        )
        
        print("Performing HTML comparison...")
        
        # Perform comparison
        result = await service.compare_html(request)
        
        print(f"\nComparison Results:")
        print(f"Comparison ID: {result.comparison_id}")
        print(f"Summary: {result.summary}")
        print(f"Total Changes: {result.total_changes}")
        print(f"Processing Time: {result.processing_time:.2f} seconds")
        print(f"Confidence Score: {result.confidence_score}")
        
        print(f"\nAdded Elements ({len(result.added_elements)}):")
        for i, element in enumerate(result.added_elements, 1):
            print(f"  {i}. {element.description}")
            if element.location:
                print(f"     Location: {element.location}")
        
        print(f"\nRemoved Elements ({len(result.removed_elements)}):")
        for i, element in enumerate(result.removed_elements, 1):
            print(f"  {i}. {element.description}")
            if element.location:
                print(f"     Location: {element.location}")
        
        print(f"\nModified Elements ({len(result.modified_elements)}):")
        for i, element in enumerate(result.modified_elements, 1):
            print(f"  {i}. {element.description}")
            if element.old_value and element.new_value:
                print(f"     Old: {element.old_value}")
                print(f"     New: {element.new_value}")
            if element.location:
                print(f"     Location: {element.location}")
        
        print("\n" + "=" * 50)
        print("Test completed successfully!")
        
        return result
        
    except Exception as e:
        print(f"Error during test: {str(e)}")
        import traceback
        traceback.print_exc()
        return None


async def test_basic_functionality():
    """Test basic functionality without LLM."""
    
    print("\nTesting Basic Functionality (Fallback Mode)")
    print("=" * 50)
    
    # Simple HTML for basic testing
    html1 = "<html><body><h1>Hello World</h1></body></html>"
    html2 = "<html><body><h1>Hello Universe</h1><p>New paragraph</p></body></html>"
    
    try:
        service = HTMLComparisonService()
        
        # Temporarily disable LLM for fallback testing
        original_llm = service.llm
        service.llm = None
        
        request = HTMLComparisonRequest(
            requested_order_html=html1,
            finalized_order_html=html2
        )
        
        result = await service.compare_html(request)
        
        print(f"Fallback comparison completed:")
        print(f"Summary: {result.summary}")
        print(f"Total Changes: {result.total_changes}")
        
        # Restore LLM
        service.llm = original_llm
        
        return True
        
    except Exception as e:
        print(f"Error during basic test: {str(e)}")
        return False


if __name__ == "__main__":
    async def main():
        # Test basic functionality first
        basic_success = await test_basic_functionality()
        
        if basic_success:
            print("\nBasic functionality test passed!")
        else:
            print("\nBasic functionality test failed!")
        
        # Test full functionality
        result = await test_html_comparison()
        
        if result:
            print("\nFull functionality test passed!")
        else:
            print("\nFull functionality test failed!")
    
    asyncio.run(main())
