#!/usr/bin/env python3
"""
Integration tests for HTML comparison functionality.
"""

import pytest
import asyncio
import sys
import os

# Add the app directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.models.schemas import HTMLComparisonRequest, HTMLComparisonResponse
from app.services.html_comparison_service import HTMLComparisonService


class TestHTMLComparisonIntegration:
    """Integration tests for HTML comparison service."""
    
    @pytest.fixture
    def service(self):
        """Create HTML comparison service instance."""
        return HTMLComparisonService()
    
    @pytest.fixture
    def sample_html_pair(self):
        """Sample HTML documents for testing."""
        original = """
        <html>
        <head><title>Original</title></head>
        <body>
            <div class="container">
                <h1>Test Document</h1>
                <p>Original content</p>
            </div>
        </body>
        </html>
        """
        
        modified = """
        <html>
        <head><title>Modified</title></head>
        <body>
            <div class="container">
                <h1>Test Document</h1>
                <p>Modified content</p>
                <p>New paragraph</p>
            </div>
        </body>
        </html>
        """
        
        return original, modified
    
    @pytest.mark.asyncio
    async def test_basic_comparison(self, service, sample_html_pair):
        """Test basic HTML comparison functionality."""
        original, modified = sample_html_pair
        
        request = HTMLComparisonRequest(
            requested_order_html=original,
            finalized_order_html=modified
        )
        
        result = await service.compare_html(request)
        
        # Verify response structure
        assert isinstance(result, HTMLComparisonResponse)
        assert result.comparison_id is not None
        assert result.timestamp is not None
        assert isinstance(result.total_changes, int)
        assert result.total_changes >= 0
        assert isinstance(result.summary, str)
        assert len(result.summary) > 0
        
        # Verify processing time is recorded
        assert result.processing_time is not None
        assert result.processing_time > 0
        
        # Verify confidence score is within valid range
        if result.confidence_score is not None:
            assert 0.0 <= result.confidence_score <= 1.0
    
    @pytest.mark.asyncio
    async def test_identical_documents(self, service):
        """Test comparison of identical documents."""
        html_content = "<html><body><h1>Same content</h1></body></html>"
        
        request = HTMLComparisonRequest(
            requested_order_html=html_content,
            finalized_order_html=html_content
        )
        
        result = await service.compare_html(request)
        
        # Should detect minimal or no changes
        assert result.total_changes >= 0
        assert isinstance(result.summary, str)
    
    @pytest.mark.asyncio
    async def test_empty_html_handling(self, service):
        """Test handling of empty or minimal HTML."""
        minimal_html1 = "<html></html>"
        minimal_html2 = "<html><body></body></html>"
        
        request = HTMLComparisonRequest(
            requested_order_html=minimal_html1,
            finalized_order_html=minimal_html2
        )
        
        result = await service.compare_html(request)
        
        # Should handle gracefully without errors
        assert isinstance(result, HTMLComparisonResponse)
        assert result.comparison_id is not None
    
    @pytest.mark.asyncio
    async def test_complex_html_comparison(self, service):
        """Test comparison of complex HTML documents."""
        complex_original = """
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <title>Order Form</title>
            <style>
                .order { margin: 20px; }
                .total { font-weight: bold; }
            </style>
        </head>
        <body>
            <div class="order">
                <h1>Order #12345</h1>
                <div class="customer">
                    <p>Customer: John Doe</p>
                    <p>Email: <EMAIL></p>
                </div>
                <div class="items">
                    <ul>
                        <li>Product A - $100</li>
                        <li>Product B - $50</li>
                    </ul>
                </div>
                <div class="total">
                    <p>Total: $150</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        complex_modified = """
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <title>Confirmed Order</title>
            <style>
                .order { margin: 20px; }
                .total { font-weight: bold; }
                .confirmed { color: green; }
            </style>
        </head>
        <body>
            <div class="order">
                <h1>Order #12345</h1>
                <div class="status confirmed">
                    <p>✓ Order Confirmed</p>
                </div>
                <div class="customer">
                    <p>Customer: John Doe</p>
                    <p>Email: <EMAIL></p>
                    <p>Phone: ******-0123</p>
                </div>
                <div class="items">
                    <ul>
                        <li>Product A - $90 (10% discount)</li>
                        <li>Product B - $50</li>
                        <li>Product C - $25 (added)</li>
                    </ul>
                </div>
                <div class="total">
                    <p>Subtotal: $165</p>
                    <p>Tax: $13.20</p>
                    <p>Total: $178.20</p>
                </div>
                <div class="shipping">
                    <p>Shipping: Express (2-3 days)</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        request = HTMLComparisonRequest(
            requested_order_html=complex_original,
            finalized_order_html=complex_modified
        )
        
        result = await service.compare_html(request)
        
        # Should detect multiple changes
        assert result.total_changes > 0
        assert len(result.summary) > 0
        
        # Should have some added elements (status, phone, shipping, etc.)
        assert len(result.added_elements) >= 0
        
        # Should have some modified elements (title, items, total)
        assert len(result.modified_elements) >= 0
    
    @pytest.mark.asyncio
    async def test_fallback_mode(self, service):
        """Test fallback mode when LLM is not available."""
        original_llm = service.llm
        service.llm = None  # Disable LLM to test fallback
        
        try:
            html1 = "<html><body><h1>Test 1</h1></body></html>"
            html2 = "<html><body><h1>Test 2</h1><p>New content</p></body></html>"
            
            request = HTMLComparisonRequest(
                requested_order_html=html1,
                finalized_order_html=html2
            )
            
            result = await service.compare_html(request)
            
            # Should work in fallback mode
            assert isinstance(result, HTMLComparisonResponse)
            assert result.comparison_id is not None
            assert result.total_changes >= 0
            
            # Confidence should be lower in fallback mode
            if result.confidence_score is not None:
                assert result.confidence_score <= 0.6
                
        finally:
            # Restore LLM
            service.llm = original_llm
    
    def test_html_cleaning(self, service):
        """Test HTML cleaning functionality."""
        messy_html = """
        <html>
        <!-- This is a comment -->
        <body>
            <h1>   Title with   extra   spaces   </h1>
            <p>
                Paragraph with
                line breaks
            </p>
        </body>
        </html>
        """
        
        cleaned = service._clean_html(messy_html)
        
        # Should be valid HTML string
        assert isinstance(cleaned, str)
        assert len(cleaned) > 0
        
        # Should not contain comments (basic check)
        assert "<!--" not in cleaned


def test_imports():
    """Test that all required modules can be imported."""
    try:
        from app.models.schemas import HTMLComparisonRequest, HTMLComparisonResponse, HTMLDifference
        from app.services.html_comparison_service import HTMLComparisonService
        from app.api.html_comparison import router
        assert True
    except ImportError as e:
        pytest.fail(f"Import failed: {e}")


if __name__ == "__main__":
    # Run tests directly
    pytest.main([__file__, "-v"])
