{"info": {"name": "HTML Comparison API", "description": "API collection for HTML document comparison using LLM analysis", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/html/health", "host": ["{{base_url}}"], "path": ["api", "v1", "html", "health"]}, "description": "Check the health and status of the HTML comparison service"}, "response": []}, {"name": "Full HTML Comparison", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"requested_order_html\": \"<!DOCTYPE html><html><head><title>Original Order</title></head><body><div class='order'><h1>Order #12345</h1><p>Customer: <PERSON></p><p>Total: $150.00</p></div></body></html>\",\n  \"finalized_order_html\": \"<!DOCTYPE html><html><head><title>Finalized Order</title></head><body><div class='order'><h1>Order #12345</h1><p>Customer: <PERSON></p><p>Phone: ******-0123</p><p>Total: $192.50</p><p>Tax: $17.50</p><div class='shipping'><p>Shipping: 123 Main St</p></div></div></body></html>\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/html/compare", "host": ["{{base_url}}"], "path": ["api", "v1", "html", "compare"]}, "description": "Perform a comprehensive comparison of two HTML documents with detailed analysis"}, "response": []}, {"name": "HTML Comparison Summary", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"requested_order_html\": \"<!DOCTYPE html><html><head><title>Original Order</title></head><body><div class='order'><h1>Order #12345</h1><p>Customer: <PERSON></p><p>Total: $150.00</p></div></body></html>\",\n  \"finalized_order_html\": \"<!DOCTYPE html><html><head><title>Finalized Order</title></head><body><div class='order'><h1>Order #12345</h1><p>Customer: <PERSON></p><p>Phone: ******-0123</p><p>Total: $192.50</p><p>Tax: $17.50</p><div class='shipping'><p>Shipping: 123 Main St</p></div></div></body></html>\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/html/compare/summary", "host": ["{{base_url}}"], "path": ["api", "v1", "html", "compare", "summary"]}, "description": "Get a quick summary of differences between two HTML documents"}, "response": []}, {"name": "Complex Order Comparison", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"requested_order_html\": \"<!DOCTYPE html><html><head><title>Order Request</title><style>.order { margin: 20px; } .customer { background: #f9f9f9; }</style></head><body><div class='order'><h1>Order #ORD-2024-001</h1><div class='customer'><h2>Customer Info</h2><p>Name: <PERSON></p><p>Email: <EMAIL></p></div><div class='items'><h2>Items</h2><ul><li>Laptop - $1200</li><li>Mouse - $25</li></ul></div><div class='total'><p>Total: $1225</p></div></div></body></html>\",\n  \"finalized_order_html\": \"<!DOCTYPE html><html><head><title>Finalized Order</title><style>.order { margin: 20px; } .customer { background: #f9f9f9; } .shipping { background: #e6f3ff; }</style></head><body><div class='order'><h1>Order #ORD-2024-001</h1><div class='status'><p style='color: green;'>✓ CONFIRMED</p></div><div class='customer'><h2>Customer Info</h2><p>Name: Alice Johnson</p><p>Email: <EMAIL></p><p>Phone: ******-0199</p></div><div class='items'><h2>Items</h2><ul><li>Laptop - $1150 (discount applied)</li><li>Mouse - $25</li><li>Warranty - $99 (added)</li></ul></div><div class='total'><p>Subtotal: $1274</p><p>Tax: $102</p><p>Total: $1376</p></div><div class='shipping'><h2>Shipping</h2><p>Address: 123 Oak St, City, State</p><p>Method: Express (2-3 days)</p></div></div></body></html>\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/html/compare", "host": ["{{base_url}}"], "path": ["api", "v1", "html", "compare"]}, "description": "Example of comparing complex order documents with multiple changes"}, "response": []}, {"name": "Error Test - Empty HTML", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"requested_order_html\": \"\",\n  \"finalized_order_html\": \"<html><body><p>Test</p></body></html>\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/html/compare", "host": ["{{base_url}}"], "path": ["api", "v1", "html", "compare"]}, "description": "Test error handling with empty HTML content"}, "response": []}], "variable": [{"key": "base_url", "value": "http://localhost:8000", "description": "Base URL for the API server"}]}