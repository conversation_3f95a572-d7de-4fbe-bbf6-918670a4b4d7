<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML Comparison Tester</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; }
        input, textarea { width: 100%; padding: 8px; box-sizing: border-box; }
        button { padding: 10px 15px; background-color: #007bff; color: white; border: none; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        .results { margin-top: 20px; }
        .error { color: red; }
        .spinner { display: none; border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; width: 20px; height: 20px; animation: spin 2s linear infinite; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
    </style>
</head>
<body>
    <div class="container">
        <h1>HTML Comparison Tester</h1>
        <div class="form-group">
            <label for="reportId">Report ID</label>
            <input type="text" id="reportId" placeholder="Enter Report ID">
        </div>
        <div class="form-group">
            <label for="bearerToken">Bearer Token</label>
            <textarea id="bearerToken" rows="4" placeholder="Enter Bearer Token"></textarea>
        </div>
        <button onclick="compareHtml()">Compare HTML</button>
        <div class="spinner" id="spinner"></div>
        <div class="results" id="results"></div>
    </div>

    <script>
        async function compareHtml() {
            const reportId = document.getElementById('reportId').value;
            const bearerToken = document.getElementById('bearerToken').value;
            const resultsDiv = document.getElementById('results');
            const spinner = document.getElementById('spinner');

            resultsDiv.innerHTML = '';
            spinner.style.display = 'block';

            if (!reportId || !bearerToken) {
                resultsDiv.innerHTML = '<p class="error">Please enter both Report ID and Bearer Token.</p>';
                spinner.style.display = 'none';
                return;
            }

            try {
                const response = await fetch('http://localhost:8000/api/v1/html/compare/by-id', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        report_id: reportId,
                        bearer_token: bearerToken
                    })
                });

                spinner.style.display = 'none';
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.detail || 'An unknown error occurred.');
                }

                let html = `<h2>Comparison Results</h2>`;
                html += `<p><strong>Comparison ID:</strong> ${data.comparison_id}</p>`;
                html += `<p><strong>Summary:</strong> ${data.summary}</p>`;
                html += `<p><strong>Total Changes:</strong> ${data.total_changes}</p>`;
                
                if (data.added_elements.length > 0) {
                    html += '<h3>Added Elements</h3>';
                    html += '<ul>';
                    data.added_elements.forEach(el => {
                        html += `<li><strong>${el.element}</strong>: ${el.description}</li>`;
                    });
                    html += '</ul>';
                }

                if (data.removed_elements.length > 0) {
                    html += '<h3>Removed Elements</h3>';
                    html += '<ul>';
                    data.removed_elements.forEach(el => {
                        html += `<li><strong>${el.element}</strong>: ${el.description}</li>`;
                    });
                    html += '</ul>';
                }

                if (data.modified_elements.length > 0) {
                    html += '<h3>Modified Elements</h3>';
                    html += '<ul>';
                    data.modified_elements.forEach(el => {
                        html += `<li><strong>${el.element}</strong>: ${el.description} (Old: ${el.old_value}, New: ${el.new_value})</li>`;
                    });
                    html += '</ul>';
                }

                resultsDiv.innerHTML = html;

            } catch (error) {
                spinner.style.display = 'none';
                resultsDiv.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
