# HTML Comparison API Documentation

The HTML Comparison API provides intelligent comparison of HTML documents using Large Language Model (LLM) analysis to identify differences between two HTML documents.

## Overview

This API is designed to compare two HTML documents (typically a requested order HTML and a finalized order HTML) and provide detailed analysis of what has been added, removed, or modified between them.

## Base URL
```
http://localhost:8000/api/v1/html
```

## Features

- **AI-Powered Analysis**: Uses OpenAI's LLM to intelligently analyze HTML differences
- **Structural Analysis**: Detects changes in HTML structure, elements, and attributes
- **Content Analysis**: Identifies text content changes and modifications
- **Detailed Reporting**: Provides comprehensive reports with specific change descriptions
- **Fallback Mode**: Basic comparison functionality when LLM is unavailable
- **Performance Metrics**: Includes processing time and confidence scores

## Endpoints

### 1. Full HTML Comparison

**POST** `/compare`

Performs a comprehensive comparison of two HTML documents with detailed analysis.

#### Request Body

```json
{
  "requested_order_html": "<!DOCTYPE html><html>...</html>",
  "finalized_order_html": "<!DOCTYPE html><html>...</html>"
}
```

#### Request Schema

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `requested_order_html` | string | Yes | The original/requested HTML content |
| `finalized_order_html` | string | Yes | The final/updated HTML content |

#### Response

```json
{
  "comparison_id": "uuid-string",
  "timestamp": "2024-01-01T12:00:00Z",
  "summary": "Brief overview of changes found",
  "total_changes": 5,
  "added_elements": [
    {
      "type": "added",
      "element": "p",
      "location": "body > div.shipping-info",
      "description": "Added shipping address paragraph",
      "old_value": null,
      "new_value": "123 Main St, City, State 12345"
    }
  ],
  "removed_elements": [
    {
      "type": "removed",
      "element": "span.old-price",
      "location": "body > div.pricing",
      "description": "Removed old price display",
      "old_value": "$100.00",
      "new_value": null
    }
  ],
  "modified_elements": [
    {
      "type": "modified",
      "element": "p.total",
      "location": "body > div.total",
      "description": "Updated total price",
      "old_value": "$150.00",
      "new_value": "$192.50"
    }
  ],
  "processing_time": 2.34,
  "confidence_score": 0.95
}
```

#### Response Schema

| Field | Type | Description |
|-------|------|-------------|
| `comparison_id` | string | Unique identifier for this comparison |
| `timestamp` | datetime | When the comparison was performed |
| `summary` | string | Overall summary of changes |
| `total_changes` | integer | Total number of changes detected |
| `added_elements` | array | List of elements that were added |
| `removed_elements` | array | List of elements that were removed |
| `modified_elements` | array | List of elements that were modified |
| `processing_time` | float | Time taken to process (seconds) |
| `confidence_score` | float | AI confidence in analysis (0.0-1.0) |

### 2. Quick Comparison Summary

**POST** `/compare/summary`

Provides a lightweight comparison with high-level statistics only.

#### Request Body

Same as full comparison endpoint.

#### Response

```json
{
  "comparison_id": "uuid-string",
  "timestamp": "2024-01-01T12:00:00Z",
  "summary": "Brief overview of changes found",
  "total_changes": 5,
  "changes_breakdown": {
    "added": 2,
    "removed": 1,
    "modified": 2
  },
  "processing_time": 1.23,
  "confidence_score": 0.95
}
```

### 3. Health Check

**GET** `/health`

Check the status and availability of the HTML comparison service.

#### Response

```json
{
  "status": "healthy",
  "service": "HTML Comparison API",
  "llm_available": true,
  "features": [
    "HTML document comparison",
    "AI-powered difference detection",
    "Structural and content analysis",
    "Detailed change reporting"
  ],
  "endpoints": [
    "/compare - Full HTML comparison with detailed results",
    "/compare/summary - Quick comparison summary",
    "/health - Service health check"
  ]
}
```

## Usage Examples

### Example 1: Basic HTML Comparison

```python
import httpx
import asyncio

async def compare_html():
    url = "http://localhost:8000/api/v1/html/compare"
    
    data = {
        "requested_order_html": """
        <html>
        <body>
            <h1>Order #12345</h1>
            <p>Total: $150</p>
        </body>
        </html>
        """,
        "finalized_order_html": """
        <html>
        <body>
            <h1>Order #12345</h1>
            <p>Total: $192.50</p>
            <p>Tax: $17.50</p>
        </body>
        </html>
        """
    }
    
    async with httpx.AsyncClient() as client:
        response = await client.post(url, json=data)
        result = response.json()
        
        print(f"Changes found: {result['total_changes']}")
        print(f"Summary: {result['summary']}")

asyncio.run(compare_html())
```

### Example 2: Quick Summary

```python
import httpx
import asyncio

async def get_summary():
    url = "http://localhost:8000/api/v1/html/compare/summary"
    
    data = {
        "requested_order_html": "<html>...</html>",
        "finalized_order_html": "<html>...</html>"
    }
    
    async with httpx.AsyncClient() as client:
        response = await client.post(url, json=data)
        result = response.json()
        
        print(f"Total changes: {result['total_changes']}")
        print(f"Breakdown: {result['changes_breakdown']}")

asyncio.run(get_summary())
```

## Error Handling

The API returns appropriate HTTP status codes and error messages:

- **400 Bad Request**: Invalid input (empty HTML content)
- **500 Internal Server Error**: Processing errors or LLM failures

Example error response:
```json
{
  "detail": "requested_order_html cannot be empty"
}
```

## Configuration

The service uses the following configuration from `app/core/config.py`:

- `OPENAI_API_KEY`: Required for LLM-powered analysis
- `OPENAI_MODEL`: Model to use (default: gpt-4o-mini)
- `MAX_TOKENS`: Maximum tokens for LLM responses

## Performance Considerations

- **Processing Time**: Varies based on HTML complexity and LLM response time
- **Token Usage**: Large HTML documents consume more tokens
- **Fallback Mode**: Available when LLM is unavailable (reduced accuracy)
- **Rate Limits**: Subject to OpenAI API rate limits

## Integration Notes

This API integrates seamlessly with the existing validation system and can be used alongside other APIs in the platform. It follows the same patterns and conventions as other services in the codebase.
